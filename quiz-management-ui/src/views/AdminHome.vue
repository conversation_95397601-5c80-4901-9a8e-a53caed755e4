<template>
  <div class="h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-black font-sans relative overflow-hidden">
    <!-- Enhanced dynamic background elements -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- Cybernetic circuit lines -->
      <div class="absolute inset-0 circuit-lines opacity-20"></div>

      <!-- Advanced animated particles with enhanced glow effect -->
      <div v-for="i in 80" :key="i"
           class="absolute rounded-full animate-float-particle"
           :class="[
             i % 6 === 0 ? 'bg-cyan-500/40' : '',
             i % 6 === 1 ? 'bg-purple-500/40' : '',
             i % 6 === 2 ? 'bg-blue-500/40' : '',
             i % 6 === 3 ? 'bg-indigo-500/40' : '',
             i % 6 === 4 ? 'bg-teal-500/40' : '',
             i % 6 === 5 ? 'bg-fuchsia-500/40' : ''
           ]"
           :style="{
             width: `${Math.random() * 15 + 2}px`,
             height: `${Math.random() * 15 + 2}px`,
             top: `${Math.random() * 100}%`,
             left: `${Math.random() * 100}%`,
             filter: 'blur(0.5px)',
             boxShadow: i % 6 === 0 ? '0 0 8px rgba(103, 232, 249, 0.7)' :
                        i % 6 === 1 ? '0 0 8px rgba(168, 85, 247, 0.7)' :
                        i % 6 === 2 ? '0 0 8px rgba(59, 130, 246, 0.7)' :
                        i % 6 === 3 ? '0 0 8px rgba(99, 102, 241, 0.7)' :
                        i % 6 === 4 ? '0 0 8px rgba(20, 184, 166, 0.7)' :
                                      '0 0 8px rgba(192, 38, 211, 0.7)',
             animation: `float-particle ${Math.random() * 30 + 20}s linear infinite`,
             animationDelay: `${Math.random() * 5}s`,
             opacity: Math.random() * 0.6 + 0.2
           }"></div>

      <!-- Enhanced glowing orbs with more depth and dimension -->
      <div v-for="i in 10" :key="`orb-${i}`"
           class="absolute rounded-full blur-3xl"
           :class="[
             i % 6 === 0 ? 'bg-cyan-600/15' : '',
             i % 6 === 1 ? 'bg-purple-600/15' : '',
             i % 6 === 2 ? 'bg-blue-600/15' : '',
             i % 6 === 3 ? 'bg-indigo-600/15' : '',
             i % 6 === 4 ? 'bg-teal-600/15' : '',
             i % 6 === 5 ? 'bg-fuchsia-600/15' : ''
           ]"
           :style="{
             width: `${Math.random() * 400 + 150}px`,
             height: `${Math.random() * 400 + 150}px`,
             top: `${Math.random() * 100}%`,
             left: `${Math.random() * 100}%`,
             animation: `pulse-glow ${Math.random() * 20 + 15}s ease-in-out infinite alternate`,
             animationDelay: `${Math.random() * 10}s`,
             transform: `rotate(${Math.random() * 360}deg)`
           }"></div>
    </div>

    <!-- Enhanced futuristic grid overlay with animated scan lines -->
    <div class="absolute inset-0 z-0">
      <div class="h-full w-full pattern-grid-lg text-cyan-400/5"></div>
      <div class="absolute inset-0 scan-line"></div>
      <div class="absolute inset-0 scan-line-horizontal"></div>
      <div class="absolute inset-0 noise-overlay"></div>
    </div>

    <!-- Intro Animation - Full Screen Logo -->
    <div v-if="!introComplete && !skipIntro" class="fixed inset-0 flex items-center justify-center z-50 bg-gray-950">
      <div class="transform scale-150 logo-glow-enhanced animate-pulse-logo">
        <pre class="whitespace-pre leading-snug text-lg">
<span class="text-gray-500">                 </span><span class="text-cyan-300 font-bold logo-text-bright">___________</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text-bright">|           \</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text-bright">|            \</span>
<span class="text-gray-500"> _   _   _____  </span><span class="text-cyan-300 font-bold logo-text-bright">|   __  __    \</span><span class="text-gray-500">  ____    _   _______</span>
<span class="text-gray-500">| | | | |  ___| </span><span class="text-cyan-300 font-bold logo-text-bright">|  |__||__|   /</span><span class="text-gray-500"> |  _ \  | | |__   __|</span>
<span class="text-gray-500">| |_| | | |_    </span><span class="text-cyan-300 font-bold logo-text-bright">|     __     /</span><span class="text-gray-500">  | |_) | | |    | |</span>
<span class="text-gray-500">|  _  | |  _|   </span><span class="text-cyan-300 font-bold logo-text-bright">|           /</span><span class="text-gray-500">   |  _ <  | |    | |</span>
<span class="text-gray-500">| | | | | |___  </span><span class="text-cyan-300 font-bold logo-text-bright">|     |\    \</span><span class="text-gray-500">   | |_) | | |    | |</span>
<span class="text-gray-500">|_| |_| |_____| </span><span class="text-cyan-300 font-bold logo-text-bright">|     | \    \</span><span class="text-gray-500">  |____/  |_|    |_|</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text-bright">|     |  \    \</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text-bright">|     |   \    \</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text-bright">|_____|    \____\</span>
        </pre>
      </div>
    </div>

    <!-- Logo animation container with enhanced effects - KEEPING HERBIT LOGO INTACT -->
    <div
      class="fixed z-50 transform origin-top-left"
      :class="[
        introAnimationStarted && !skipIntro ? 'animate-logo-move-to-corner' : skipIntro ? 'top-3 left-3 scale-40' : 'opacity-0',
        introComplete || skipIntro ? 'top-3 left-3 scale-40 corner-logo-glow' : 'top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 scale-150'
      ]"
    >
      <pre class="whitespace-pre leading-snug logo-glow-enhanced text-xs">
<span class="text-gray-500">                 </span><span class="text-cyan-300 font-bold logo-text-bright">___________</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text-bright">|           \</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text-bright">|            \</span>
<span class="text-gray-500"> _   _   _____  </span><span class="text-cyan-300 font-bold logo-text-bright">|   __  __    \</span><span class="text-gray-500">  ____    _   _______</span>
<span class="text-gray-500">| | | | |  ___| </span><span class="text-cyan-300 font-bold logo-text-bright">|  |__||__|   /</span><span class="text-gray-500"> |  _ \  | | |__   __|</span>
<span class="text-gray-500">| |_| | | |_    </span><span class="text-cyan-300 font-bold logo-text-bright">|     __     /</span><span class="text-gray-500">  | |_) | | |    | |</span>
<span class="text-gray-500">|  _  | |  _|   </span><span class="text-cyan-300 font-bold logo-text-bright">|           /</span><span class="text-gray-500">   |  _ <  | |    | |</span>
<span class="text-gray-500">| | | | | |___  </span><span class="text-cyan-300 font-bold logo-text-bright">|     |\    \</span><span class="text-gray-500">   | |_) | | |    | |</span>
<span class="text-gray-500">|_| |_| |_____| </span><span class="text-cyan-300 font-bold logo-text-bright">|     | \    \</span><span class="text-gray-500">  |____/  |_|    |_|</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text-bright">|     |  \    \</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text-bright">|     |   \    \</span>
<span class="text-gray-500">                </span><span class="text-cyan-300 font-bold logo-text-bright">|_____|    \____\</span>
      </pre>
    </div>

    <!-- System Status Bar - moved to right side only -->
    <div v-if="introComplete || skipIntro" class="fixed top-4 right-4 z-50 flex items-center space-x-4" :class="{'animate-fade-in': !skipIntro}">
      <div class="flex items-center space-x-2 bg-gray-900/70 backdrop-blur-md px-3 py-1.5 rounded-full border border-cyan-500/30 shadow-glow-sm">
        <div class="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
        <span class="text-xs text-gray-300">System Online</span>
      </div>
      <div class="bg-gray-900/70 backdrop-blur-md px-3 py-1.5 rounded-full border border-cyan-500/30 shadow-glow-sm">
        <span class="text-xs text-gray-300">{{ currentTime }}</span>
      </div>
    </div>

    <!-- Main Content - fixed height with no scrolling -->
    <div v-if="introComplete || skipIntro" class="h-screen flex flex-col items-center justify-center relative z-10" :class="{'animate-content-fade-in': !skipIntro}">
      <!-- Welcome Message -->
      <div class="w-full max-w-4xl mb-8 text-center">
        <h1 class="text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-500 mb-3">Welcome to HERBIT</h1>
        <p class="text-gray-400 text-base max-w-2xl mx-auto">Access and manage all assessment tools and resources from this central dashboard</p>
        <div class="w-full max-w-md mx-auto h-1 bg-gradient-to-r from-transparent via-cyan-500 to-transparent mt-4 opacity-60"></div>
      </div>



      <!-- Main Menu Options with enhanced 3D cards -->
      <div class="grid grid-cols-2 gap-6 max-w-5xl w-full">
        <!-- Assessments Management Card -->
        <div class="relative group perspective h-[280px]">
          <div class="absolute -inset-0.5 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-lg blur opacity-30 group-hover:opacity-100 transition duration-300"></div>
          <div class="relative bg-gray-900/80 backdrop-blur-sm rounded-lg p-5 shadow-xl transform transition-all duration-500 group-hover:translate-z-10 border border-gray-800 group-hover:border-cyan-500/50 h-full flex flex-col">
            <div class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-cyan-500/20 to-blue-600/20 rounded-bl-full"></div>
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center mr-4 shadow-glow">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-white">Assessments Management</h2>
            </div>
            <div class="space-y-3 pl-16 flex-grow">
              <Button @click="navigateTo('/create-assessment')" variant="homeCardNav" size="homeCardNav" class="hover:from-cyan-900/50 hover:to-blue-900/50 !flex !items-center !flex-row !justify-start">
                <div class="flex items-center justify-start w-full">
                  <ArrowIcon :size="24" class="mr-3 flex-shrink-0 text-cyan-400 group-hover:text-cyan-300 transition-all duration-300 group-hover:translate-x-1" />
                  <span class="text-left">Create Assessments</span>
                </div>
              </Button>
            </div>
          </div>
        </div>

        <!-- Sessions Management Card -->
        <div class="relative group perspective h-[280px]">
          <div class="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg blur opacity-30 group-hover:opacity-100 transition duration-300"></div>
          <div class="relative bg-gray-900/80 backdrop-blur-sm rounded-lg p-5 shadow-xl transform transition-all duration-500 group-hover:translate-z-10 border border-gray-800 group-hover:border-purple-500/50 h-full flex flex-col">
            <div class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-purple-500/20 to-indigo-600/20 rounded-bl-full"></div>
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 flex items-center justify-center mr-4 shadow-glow">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-white">Sessions Management</h2>
            </div>
            <div class="space-y-3 pl-16 flex-grow">
              <Button @click="navigateTo('/generate-sessions')" variant="homeCardNav" size="homeCardNav" class="hover:from-purple-900/50 hover:to-indigo-900/50 !flex !items-center !flex-row !justify-start">
                <div class="flex items-center justify-start w-full">
                  <ArrowIcon :size="24" class="mr-3 flex-shrink-0 text-purple-400 group-hover:text-purple-300 transition-all duration-300 group-hover:translate-x-2" />
                  <span class="text-left">Generate Session Codes for an Assessment</span>
                </div>
              </Button>
              <Button @click="navigateTo('/generate-link')" variant="homeCardNav" size="homeCardNav" class="hover:from-purple-900/50 hover:to-indigo-900/50 !flex !items-center !flex-row !justify-start">
                <div class="flex items-center justify-start w-full">
                  <ArrowIcon :size="24" class="mr-3 flex-shrink-0 text-purple-400 group-hover:text-purple-300 transition-all duration-300 group-hover:translate-x-2" />
                  <span class="text-left">Generate Link</span>
                </div>
              </Button>
            </div>
          </div>
        </div>

        <!-- Skills Management Card -->
        <div class="relative group perspective h-[280px]">
          <div class="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg blur opacity-30 group-hover:opacity-100 transition duration-300"></div>
          <div class="relative bg-gray-900/80 backdrop-blur-sm rounded-lg p-5 shadow-xl transform transition-all duration-500 group-hover:translate-z-10 border border-gray-800 group-hover:border-blue-500/50 h-full flex flex-col">
            <div class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 rounded-bl-full"></div>
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center mr-4 shadow-glow">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-white">Skills Management</h2>
            </div>
            <div class="space-y-3 pl-16 flex-grow">
              <Button @click="navigateTo('/create-skill')" variant="homeCardNav" size="homeCardNav" class="hover:from-blue-900/50 hover:to-indigo-900/50 !flex !items-center !flex-row !justify-start">
                <div class="flex items-center justify-start w-full">
                  <ArrowIcon :size="24" class="mr-3 flex-shrink-0 text-blue-400 group-hover:text-blue-300 transition-all duration-300 group-hover:translate-x-1" />
                  <span class="text-left">Create a Skill</span>
                </div>
              </Button>
              <Button @click="navigateTo('/list-skills')" variant="homeCardNav" size="homeCardNav" class="hover:from-blue-900/50 hover:to-indigo-900/50 !flex !items-center !flex-row !justify-start">
                <div class="flex items-center justify-start w-full">
                  <ArrowIcon :size="24" class="mr-3 flex-shrink-0 text-blue-400 group-hover:text-blue-300 transition-all duration-300 group-hover:translate-x-1" />
                  <span class="text-left">List Skills</span>
                </div>
              </Button>
            </div>
          </div>
        </div>

        <!-- Reports Generation Card -->
        <div class="relative group perspective h-[280px]">
          <div class="absolute -inset-0.5 bg-gradient-to-r from-teal-500 to-cyan-600 rounded-lg blur opacity-30 group-hover:opacity-100 transition duration-300"></div>
          <div class="relative bg-gray-900/80 backdrop-blur-sm rounded-lg p-5 shadow-xl transform transition-all duration-500 group-hover:translate-z-10 border border-gray-800 group-hover:border-teal-500/50 h-full flex flex-col">
            <div class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-teal-500/20 to-cyan-600/20 rounded-bl-full"></div>
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 rounded-full bg-gradient-to-r from-teal-500 to-cyan-600 flex items-center justify-center mr-4 shadow-glow">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-white">Reports Generation</h2>
            </div>
            <div class="space-y-3 pl-16 flex-grow">
              <Button @click="navigateTo('/date-wise-report')" variant="homeCardNav" size="homeCardNav" class="hover:from-teal-900/50 hover:to-cyan-900/50 !flex !items-center !flex-row !justify-start">
                <div class="flex items-center justify-start w-full">
                  <ArrowIcon :size="24" class="mr-3 flex-shrink-0 text-teal-400 group-hover:text-teal-300 transition-all duration-300 group-hover:translate-x-1" />
                  <span class="text-left">Date-wise Report</span>
                </div>
              </Button>
              <Button @click="navigateTo('/user-wise-report')" variant="homeCardNav" size="homeCardNav" class="hover:from-teal-900/50 hover:to-cyan-900/50 !flex !items-center !flex-row !justify-start">
                <div class="flex items-center justify-start w-full">
                  <ArrowIcon :size="24" class="mr-3 flex-shrink-0 text-teal-400 group-hover:text-teal-300 transition-all duration-300 group-hover:translate-x-1" />
                  <span class="text-left">User-wise Report</span>
                </div>
              </Button>
              <Button @click="navigateTo('/assessment-wise-report')" variant="homeCardNav" size="homeCardNav" class="hover:from-teal-900/50 hover:to-cyan-900/50 !flex !items-center !flex-row !justify-start">
                <div class="flex items-center justify-start w-full">
                  <ArrowIcon :size="24" class="mr-3 flex-shrink-0 text-teal-400 group-hover:text-teal-300 transition-all duration-300 group-hover:translate-x-1" />
                  <span class="text-left">Assessment-wise Report</span>
                </div>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="w-full max-w-4xl mt-6">
        <h3 class="text-xl font-bold text-white mb-4 flex items-center">
          <span class="mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </span>
          Quick Actions
        </h3>
        <div class="grid grid-cols-4 gap-4">
          <Button @click="navigateTo('/create-assessment')" variant="quickAction" size="quickAction" class="hover:border-cyan-500/50">
            <div class="text-cyan-400 mb-2 group-hover:text-cyan-300 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <span class="text-sm text-gray-300 text-center">New Assessment</span>
          </Button>
          <Button @click="navigateTo('/generate-sessions')" variant="quickAction" size="quickAction" class="hover:border-purple-500/50">
            <div class="text-purple-400 mb-2 group-hover:text-purple-300 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 0 21 9z" />
              </svg>
            </div>
            <span class="text-sm text-gray-300 text-center">Generate Sessions</span>
          </Button>
          <Button @click="navigateTo('/create-skill')" variant="quickAction" size="quickAction" class="hover:border-blue-500/50">
            <div class="text-blue-400 mb-2 group-hover:text-blue-300 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <span class="text-sm text-gray-300 text-center">Create Skill</span>
          </Button>
          <Button @click="navigateTo('/date-wise-report')" variant="quickAction" size="quickAction" class="hover:border-teal-500/50">
            <div class="text-teal-400 mb-2 group-hover:text-teal-300 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <span class="text-sm text-gray-300 text-center">Generate Report</span>
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { navigateTo as navigate } from '@/utils/navigation';
import { Button } from '@/components/ui/button';
import { ArrowIcon } from '@/components/icons';

const router = useRouter();

const navigateTo = (path) => {
  navigate(router, path);
};

// Animation state variables
const introComplete = ref(false);
const introAnimationStarted = ref(false);
const skipIntro = ref(false);

// Current time display
const currentTime = computed(() => {
  const now = new Date();
  return now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
});

// Update time every minute
let timeInterval;
let introTimeout;

onMounted(() => {
  // Set up time interval for clock
  timeInterval = setInterval(() => {
    // Force computed property to update
  }, 60000);

  // For detecting page refresh, we'll use a timestamp approach
  const lastPathKey = 'lastPath';
  const lastTimeKey = 'lastTime';
  const navigationSourceKey = 'navigationSource';

  const currentPath = window.location.pathname;
  const currentTime = new Date().getTime();
  const lastPath = localStorage.getItem(lastPathKey);
  const lastTime = localStorage.getItem(lastTimeKey);
  const navigationSource = localStorage.getItem(navigationSourceKey);

  // Check for URL parameter to force the intro (for testing)
  const urlParams = new URLSearchParams(window.location.search);
  const forceIntro = urlParams.get('showIntro') === 'true';

  // Determine if this is a page refresh
  const isRefresh = currentPath === lastPath &&
                   (document.referrer === '' ||
                    (lastTime && currentTime - parseInt(lastTime) < 2000));

  // Only show intro on direct refresh of the home page or first visit
  // Do not show when navigating from another page within the app
  const shouldShowIntro = (isRefresh || !lastPath || forceIntro) && navigationSource !== 'internal';



  // Update the storage with current values
  localStorage.setItem(lastPathKey, currentPath);
  localStorage.setItem(lastTimeKey, currentTime.toString());

  // Clear the navigation source after we've used it for this decision
  localStorage.removeItem(navigationSourceKey);

  if (shouldShowIntro) {
    // This is either first load or a refresh - show the intro

    // Start intro animation after 5 seconds
    introTimeout = setTimeout(() => {
      introAnimationStarted.value = true;

      // After animation completes (1s for the animation), show the content
      setTimeout(() => {
        introComplete.value = true;
      }, 1000);
    }, 3000);
  } else {
    // This is navigation from another page - skip the intro
    skipIntro.value = true;
    introComplete.value = true;
    introAnimationStarted.value = true;
  }
});

// Clean up intervals when component is unmounted
onUnmounted(() => {
  if (timeInterval) clearInterval(timeInterval);
  if (introTimeout) clearTimeout(introTimeout);
});


</script>

<style scoped>
/* Enhanced logo and content animations */
@keyframes logo-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(5px);
  }
  50% {
    filter: blur(0);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes tagline-fade-in {
  0% {
    opacity: 0;
    transform: translateY(15px);
    filter: blur(3px);
  }
  60% {
    filter: blur(0);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

@keyframes logo-move-to-corner {
  0% {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(1);
    filter: drop-shadow(0 0 15px rgba(34, 211, 238, 0.3));
  }
  100% {
    position: fixed;
    top: 12px;
    left: 12px;
    transform: translate(0, 0) scale(0.4);
    margin: 0;
    padding: 0;
    filter: drop-shadow(0 2px 8px rgba(34, 211, 238, 0.4));
  }
}

@keyframes content-fade-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.98);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* Enhanced particle animations */
@keyframes float-particle {
  0% {
    transform: translateY(0) rotate(0deg) scale(0.8);
    opacity: 0;
  }
  10% {
    opacity: 0.3;
    transform: translateY(-10vh) rotate(45deg) scale(1);
  }
  50% {
    transform: translateY(-50vh) rotate(180deg) scale(1.2);
    opacity: 0.6;
  }
  90% {
    opacity: 0.2;
    transform: translateY(-90vh) rotate(315deg) scale(1);
  }
  100% {
    transform: translateY(-100vh) rotate(360deg) scale(0.8);
    opacity: 0;
  }
}

@keyframes pulse-glow {
  0% {
    opacity: 0.05;
    transform: scale(0.92) rotate(0deg);
    filter: blur(40px);
  }
  50% {
    opacity: 0.15;
    filter: blur(60px);
  }
  100% {
    opacity: 0.08;
    transform: scale(1.08) rotate(5deg);
    filter: blur(50px);
  }
}

/* Scan line animations */
@keyframes scan-line {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.1;
  }
  90% {
    opacity: 0.5;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes scan-line-horizontal {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  10% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.1;
  }
  90% {
    opacity: 0.3;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes pulse-text {
  0%, 100% {
    text-shadow: 0 0 8px rgba(34, 211, 238, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(34, 211, 238, 0.8);
  }
}

@keyframes pulse-logo {
  0%, 100% {
    filter: drop-shadow(0 0 35px rgba(34, 211, 238, 0.8));
    transform: scale(1);
  }
  50% {
    filter: drop-shadow(0 0 60px rgba(34, 211, 238, 1));
    transform: scale(1.05);
  }
}

/* Special animation for the corner logo with extra strong glow */
@keyframes corner-logo-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 50px rgba(34, 211, 238, 0.9));
  }
  50% {
    filter: drop-shadow(0 0 80px rgba(34, 211, 238, 1)) drop-shadow(0 0 120px rgba(34, 211, 238, 0.6));
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Enhanced grid pattern */
.pattern-grid-lg {
  background-image:
    linear-gradient(to right, currentColor 1px, transparent 1px),
    linear-gradient(to bottom, currentColor 1px, transparent 1px);
  background-size: 40px 40px;
}

/* Scan line effects */
.scan-line {
  background: linear-gradient(to bottom,
    transparent 0%,
    rgba(34, 211, 238, 0.15) 50%,
    transparent 100%);
  height: 200px;
  width: 100%;
  animation: scan-line 8s linear infinite;
  pointer-events: none;
}

.scan-line-horizontal {
  background: linear-gradient(to right,
    transparent 0%,
    rgba(34, 211, 238, 0.1) 50%,
    transparent 100%);
  height: 100%;
  width: 200px;
  animation: scan-line-horizontal 12s linear infinite;
  animation-delay: 2s;
  pointer-events: none;
}

/* Circuit lines background */
.circuit-lines {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10,10 L30,10 L30,30 L50,30 L50,50 L70,50 L70,70 L90,70' stroke='rgba(34, 211, 238, 0.15)' fill='none' stroke-width='0.5'/%3E%3Cpath d='M90,10 L70,10 L70,30 L50,30 L50,50 L30,50 L30,70 L10,70' stroke='rgba(34, 211, 238, 0.15)' fill='none' stroke-width='0.5'/%3E%3Ccircle cx='10' cy='10' r='2' fill='rgba(34, 211, 238, 0.2)'/%3E%3Ccircle cx='30' cy='30' r='2' fill='rgba(34, 211, 238, 0.2)'/%3E%3Ccircle cx='50' cy='50' r='2' fill='rgba(34, 211, 238, 0.2)'/%3E%3Ccircle cx='70' cy='70' r='2' fill='rgba(34, 211, 238, 0.2)'/%3E%3Ccircle cx='90' cy='10' r='2' fill='rgba(34, 211, 238, 0.2)'/%3E%3Ccircle cx='70' cy='30' r='2' fill='rgba(34, 211, 238, 0.2)'/%3E%3Ccircle cx='30' cy='70' r='2' fill='rgba(34, 211, 238, 0.2)'/%3E%3Ccircle cx='10' cy='70' r='2' fill='rgba(34, 211, 238, 0.2)'/%3E%3Ccircle cx='90' cy='70' r='2' fill='rgba(34, 211, 238, 0.2)'/%3E%3C/svg%3E");
  background-size: 100px 100px;
}

/* Noise overlay */
.noise-overlay {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.05'/%3E%3C/svg%3E");
  opacity: 0.2;
  mix-blend-mode: overlay;
}

/* Logo glow effect */
.logo-glow {
  filter: drop-shadow(0 0 15px rgba(34, 211, 238, 0.3));
}

.logo-glow-enhanced {
  filter: drop-shadow(0 0 45px rgba(34, 211, 238, 0.9));
}

/* Extra strong glow for corner logo */
.corner-logo-glow {
  animation: corner-logo-pulse 3s ease-in-out infinite;
}

.corner-logo-glow .logo-text-bright {
  text-shadow: 0 0 30px rgba(34, 211, 238, 1), 0 0 60px rgba(34, 211, 238, 0.9), 0 0 90px rgba(34, 211, 238, 0.6);
}

.logo-text {
  text-shadow: 0 0 10px rgba(34, 211, 238, 0.5);
}

.logo-text-bright {
  text-shadow: 0 0 25px rgba(34, 211, 238, 1), 0 0 50px rgba(34, 211, 238, 0.8), 0 0 70px rgba(34, 211, 238, 0.4);
}

/* Tagline glow effect */
.tagline-glow {
  text-shadow: 0 0 20px rgba(34, 211, 238, 0.2);
}

/* Pulse text animation */
.pulse-text {
  animation: pulse-text 2s ease-in-out infinite;
}

/* 3D perspective */
.perspective {
  perspective: 1000px;
}

.translate-z-10 {
  transform: translateZ(10px);
}

/* Shadow glow effects */
.shadow-glow {
  box-shadow: 0 0 15px rgba(34, 211, 238, 0.5);
}

.shadow-glow-sm {
  box-shadow: 0 0 8px rgba(34, 211, 238, 0.2);
}

/* Scale utility for logo */
.scale-35 {
  transform: scale(0.35) !important;
}
.scale-40 {
  transform: scale(0.5) !important;
}
.scale-45 {
  transform: scale(0.45) !important;
}

/* Padding for sub-options */
.pl-13 {
  padding-left: 3.25rem;
}

.pl-16 {
  padding-left: 4rem;
}

/* Animation classes */
.animate-logo-fade-in {
  animation: logo-fade-in 1.5s ease-out forwards;
}

.animate-tagline-fade-in {
  animation: tagline-fade-in 1.5s ease-out forwards;
  animation-delay: 0.5s;
  opacity: 0;
}

.animate-logo-move-to-corner {
  animation: logo-move-to-corner 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.animate-content-fade-in {
  animation: content-fade-in 1s ease-out forwards;
  animation-delay: 0.3s;
}

.animate-pulse-logo {
  animation: pulse-logo 2s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}
</style>
