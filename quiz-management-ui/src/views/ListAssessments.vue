<template>
  <HerbitProfessionalLayout
    title="Assessments List"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Assessments Table Card -->
    <div class="max-w-5xl mx-auto">
      <Card variant="table" color="blue" hover>
        <!-- Loading indicator -->
        <div v-if="isLoading" class="flex justify-center items-center py-12">
          <SpinnerIcon :size="48" />
          <span class="ml-3 text-gray-300">Loading assessments...</span>
        </div>

        <!-- Error message -->
        <div v-else-if="message && !isSuccess" class="p-6">
          <Alert variant="destructive">
            <AlertDescription>{{ message }}</AlertDescription>
          </Alert>
        </div>

        <!-- Assessments table -->
        <div v-else-if="assessments.length > 0" class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-gray-700">
                <th class="text-left py-4 px-6 text-gray-300 font-semibold">Name</th>
                <th class="text-left py-4 px-6 text-gray-300 font-semibold">Description</th>
                <th class="text-left py-4 px-6 text-gray-300 font-semibold">Mode</th>
                <th class="text-left py-4 px-6 text-gray-300 font-semibold">Questions</th>
                <th class="text-left py-4 px-6 text-gray-300 font-semibold">Duration</th>
                <th class="text-left py-4 px-6 text-gray-300 font-semibold">Created</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="assessment in assessments"
                :key="assessment.id"
                class="border-b border-gray-800 hover:bg-gray-800/50 transition-colors cursor-pointer"
                @click="viewAssessmentDetails(assessment.id)"
              >
                <td class="py-4 px-6">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <div>
                      <div class="text-white font-medium">{{ assessment.name }}</div>
                    </div>
                  </div>
                </td>
                <td class="py-4 px-6">
                  <div class="text-gray-300 max-w-xs truncate">
                    {{ assessment.description || 'No description' }}
                  </div>
                </td>
                <td class="py-4 px-6">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="assessment.question_selection_mode === 'fixed'
                      ? 'bg-purple-100 text-purple-800'
                      : 'bg-green-100 text-green-800'"
                  >
                    {{ assessment.question_selection_mode === 'fixed' ? 'Fixed' : 'Dynamic' }}
                  </span>
                </td>
                <td class="py-4 px-6">
                  <div class="text-gray-300">
                    {{ assessment.total_questions || 'N/A' }}
                  </div>
                </td>
                <td class="py-4 px-6">
                  <div class="text-gray-300">
                    {{ assessment.duration_minutes ? `${assessment.duration_minutes} min` : 'N/A' }}
                  </div>
                </td>
                <td class="py-4 px-6">
                  <div class="text-gray-400 text-sm">
                    {{ formatDate(assessment.created_at) }}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty state -->
        <div v-else class="text-center py-12">
          <div class="text-gray-400 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-300 mb-2">No assessments found</h3>
          <p class="text-gray-400 mb-6">Get started by creating your first assessment.</p>
          <Button @click="navigateToCreateAssessment" variant="default" size="default">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create Assessment
            </div>
          </Button>
        </div>
      </Card>
    </div>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { navigateTo } from '@/utils/navigation';
import { HerbitProfessionalLayout } from '@/components/layout';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SpinnerIcon } from '@/components/icons';

const router = useRouter();

// Message handling
const { message, isSuccess, setErrorMessage, clearMessage } = useMessageHandler();

// Component state
const assessments = ref([]);
const isLoading = ref(false);

// Fetch assessments from API
const fetchAssessments = async () => {
  try {
    isLoading.value = true;
    clearMessage();

    const response = await api.admin.getAssessments();
    assessments.value = response.data.assessments || [];
  } catch (error) {
    logError(error, 'fetchAssessments');
    setErrorMessage(getErrorMessage(error, 'Failed to load assessments'));
  } finally {
    isLoading.value = false;
  }
};

// Format date for display
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return 'Invalid date';
  }
};

// Navigation functions
const navigateToCreateAssessment = () => {
  navigateTo(router, '/create-assessment');
};

const viewAssessmentDetails = (assessmentId) => {
  // For now, just log the ID. In the future, this could navigate to a detail page
  console.log('View assessment details for ID:', assessmentId);
  // navigateTo(router, `/assessment/${assessmentId}`);
};

// Load assessments when component mounts
onMounted(() => {
  fetchAssessments();
});
</script>

<style scoped>
/* Custom scrollbar styling for dark theme */
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5); /* gray-800 with opacity */
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #06b6d4, #3b82f6); /* cyan to blue gradient */
  border-radius: 4px;
  border: 1px solid rgba(55, 65, 81, 0.3); /* subtle border */
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #0891b2, #2563eb); /* darker gradient on hover */
  box-shadow: 0 0 8px rgba(6, 182, 212, 0.3); /* subtle glow effect */
}

/* Firefox scrollbar styling */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: #06b6d4 rgba(31, 41, 55, 0.5);
}

/* Global scrollbar styling for the entire page */
:global(html) {
  scrollbar-width: thin;
  scrollbar-color: #06b6d4 rgba(31, 41, 55, 0.5);
}

:global(body)::-webkit-scrollbar {
  width: 12px;
}

:global(body)::-webkit-scrollbar-track {
  background: rgba(17, 24, 39, 0.8); /* gray-900 with opacity */
  border-radius: 6px;
}

:global(body)::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #06b6d4, #3b82f6); /* cyan to blue gradient */
  border-radius: 6px;
  border: 2px solid rgba(17, 24, 39, 0.8);
}

:global(body)::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #0891b2, #2563eb); /* darker gradient on hover */
  box-shadow: 0 0 12px rgba(6, 182, 212, 0.4); /* glow effect */
}
</style>
